<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eComQnA Chat Widget Demo</title>
</head>
<body>
    <h1>eComQnA Chat Widget Integration Demo</h1>
    <p>This page demonstrates how to integrate the eComQnA floating chat widget.</p>
    
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. N<PERSON><PERSON> auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>
    
    <p>More content here to show how the chat widget appears on a real page...</p>

    <!-- eComQnA Chatbot -->
    <script>
      window.eComQnAConfig = {
        projectId: 'OpspRpoxr7bKIu2FjpMK',
        themeColor: '#4f46e5',
        position: 'bottom-right',
        baseUrl: 'http://localhost:5173'
      };
    </script>
    <script src="http://localhost:5173/embed.js" async></script>
</body>
</html>