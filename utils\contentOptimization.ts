/**
 * Content optimization utilities for knowledge base processing
 */

export interface ContentChunk {
  id: string;
  content: string;
  hash: string;
  sourceId: string;
  sourceType: string;
  size: number;
  metadata?: Record<string, any>;
}

export interface OptimizationOptions {
  enableDeduplication?: boolean;
  maxChunkSize?: number;
  chunkOverlap?: number;
  minChunkSize?: number;
  preserveFormatting?: boolean;
  removeEmptyLines?: boolean;
}

export interface OptimizationResult {
  originalChunks: number;
  optimizedChunks: number;
  duplicatesRemoved: number;
  totalSizeReduction: number;
  processingTime: number;
}

export class ContentOptimizer {
  private static readonly DEFAULT_OPTIONS: Required<OptimizationOptions> = {
    enableDeduplication: true,
    maxChunkSize: 4000,
    chunkOverlap: 200,
    minChunkSize: 100,
    preserveFormatting: false,
    removeEmptyLines: true,
  };

  private static memoryUsage = {
    currentUsage: 0,
    peakUsage: 0,
    maxAllowed: 100 * 1024 * 1024, // 100MB default
  };

  /**
   * Generate a simple hash for content deduplication
   */
  private static generateHash(content: string): string {
    let hash = 0;
    if (content.length === 0) return hash.toString();
    
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Clean and normalize content
   */
  private static cleanContent(content: string, options: Required<OptimizationOptions>): string {
    let cleaned = content;

    // Remove excessive whitespace
    if (options.removeEmptyLines) {
      cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n'); // Replace multiple empty lines with double newline
      cleaned = cleaned.replace(/[ \t]+/g, ' '); // Replace multiple spaces/tabs with single space
    }

    // Preserve or normalize formatting
    if (!options.preserveFormatting) {
      cleaned = cleaned.replace(/\r\n/g, '\n'); // Normalize line endings
      cleaned = cleaned.trim(); // Remove leading/trailing whitespace
    }

    return cleaned;
  }

  /**
   * Split content into chunks with overlap
   */
  private static createChunks(
    content: string,
    sourceId: string,
    sourceType: string,
    options: Required<OptimizationOptions>
  ): ContentChunk[] {
    const chunks: ContentChunk[] = [];
    const maxSize = options.maxChunkSize;
    const overlap = options.chunkOverlap;
    const minSize = options.minChunkSize;

    if (content.length <= maxSize) {
      // Content fits in a single chunk
      if (content.length >= minSize) {
        chunks.push({
          id: `${sourceId}-chunk-0`,
          content,
          hash: this.generateHash(content),
          sourceId,
          sourceType,
          size: content.length,
          metadata: { chunkIndex: 0, totalChunks: 1 }
        });
      }
      return chunks;
    }

    // Split content into overlapping chunks
    let startIndex = 0;
    let chunkIndex = 0;

    while (startIndex < content.length) {
      const endIndex = Math.min(startIndex + maxSize, content.length);
      let chunkContent = content.substring(startIndex, endIndex);

      // Try to break at word boundaries for better readability
      if (endIndex < content.length) {
        const lastSpaceIndex = chunkContent.lastIndexOf(' ');
        const lastNewlineIndex = chunkContent.lastIndexOf('\n');
        const breakIndex = Math.max(lastSpaceIndex, lastNewlineIndex);
        
        if (breakIndex > chunkContent.length * 0.8) {
          // If we can break at a reasonable position, do so
          chunkContent = chunkContent.substring(0, breakIndex);
        }
      }

      // Only add chunk if it meets minimum size requirement
      if (chunkContent.trim().length >= minSize) {
        chunks.push({
          id: `${sourceId}-chunk-${chunkIndex}`,
          content: chunkContent.trim(),
          hash: this.generateHash(chunkContent.trim()),
          sourceId,
          sourceType,
          size: chunkContent.length,
          metadata: { 
            chunkIndex, 
            startIndex, 
            endIndex: startIndex + chunkContent.length 
          }
        });
        chunkIndex++;
      }

      // Move start index forward, accounting for overlap
      const actualChunkSize = chunkContent.length;
      startIndex += Math.max(actualChunkSize - overlap, minSize);
    }

    // Update total chunks metadata
    chunks.forEach(chunk => {
      if (chunk.metadata) {
        chunk.metadata.totalChunks = chunks.length;
      }
    });

    return chunks;
  }

  /**
   * Create chunks with memory management for large content
   */
  private static createChunksWithMemoryManagement(
    content: string,
    sourceId: string,
    sourceType: string,
    options: Required<OptimizationOptions>
  ): ContentChunk[] {
    const chunks: ContentChunk[] = [];
    const maxSize = options.maxChunkSize;
    const overlap = options.chunkOverlap;
    const minSize = options.minChunkSize;

    // For very large content, process in smaller batches
    const BATCH_SIZE = 50000; // Process 50KB at a time

    if (content.length <= BATCH_SIZE) {
      return this.createChunks(content, sourceId, sourceType, options);
    }

    // Process large content in batches
    let processedLength = 0;
    let chunkIndex = 0;

    while (processedLength < content.length) {
      const batchEnd = Math.min(processedLength + BATCH_SIZE + overlap, content.length);
      const batchContent = content.substring(processedLength, batchEnd);

      // Track memory for this batch
      this.trackMemoryUsage('batch_processing', batchContent.length * 2);

      try {
        // Create chunks for this batch
        const batchChunks = this.createChunks(
          batchContent,
          `${sourceId}-batch-${Math.floor(processedLength / BATCH_SIZE)}`,
          sourceType,
          options
        );

        // Adjust chunk IDs to be globally unique
        batchChunks.forEach(chunk => {
          chunk.id = `${sourceId}-chunk-${chunkIndex}`;
          chunk.metadata = {
            ...chunk.metadata,
            chunkIndex,
            globalStartIndex: processedLength + (chunk.metadata?.startIndex || 0),
            globalEndIndex: processedLength + (chunk.metadata?.endIndex || 0),
          };
          chunkIndex++;
        });

        chunks.push(...batchChunks);

        // Free memory for this batch
        this.trackMemoryUsage('batch_cleanup', batchContent.length * 2, false);

      } catch (error) {
        console.error(`Error processing batch at position ${processedLength}:`, error);
        this.trackMemoryUsage('batch_cleanup', batchContent.length * 2, false);
      }

      processedLength += BATCH_SIZE;
    }

    // Update total chunks metadata
    chunks.forEach(chunk => {
      if (chunk.metadata) {
        chunk.metadata.totalChunks = chunks.length;
      }
    });

    return chunks;
  }

  /**
   * Remove duplicate chunks based on content hash
   */
  private static deduplicateChunks(chunks: ContentChunk[]): {
    uniqueChunks: ContentChunk[];
    duplicatesRemoved: number;
  } {
    const seenHashes = new Set<string>();
    const uniqueChunks: ContentChunk[] = [];
    let duplicatesRemoved = 0;

    for (const chunk of chunks) {
      if (!seenHashes.has(chunk.hash)) {
        seenHashes.add(chunk.hash);
        uniqueChunks.push(chunk);
      } else {
        duplicatesRemoved++;
      }
    }

    return { uniqueChunks, duplicatesRemoved };
  }

  /**
   * Track memory usage
   */
  private static trackMemoryUsage(operation: string, size: number, isAllocation: boolean = true): void {
    if (isAllocation) {
      this.memoryUsage.currentUsage += size;
      this.memoryUsage.peakUsage = Math.max(this.memoryUsage.peakUsage, this.memoryUsage.currentUsage);
    } else {
      this.memoryUsage.currentUsage = Math.max(0, this.memoryUsage.currentUsage - size);
    }

    if (this.memoryUsage.currentUsage > this.memoryUsage.maxAllowed) {
      console.warn(`Memory usage exceeded limit: ${this.memoryUsage.currentUsage} bytes (${operation})`);
    }
  }

  /**
   * Get current memory usage statistics
   */
  public static getMemoryStats(): {
    current: number;
    peak: number;
    maxAllowed: number;
    utilizationPercentage: number;
  } {
    return {
      current: this.memoryUsage.currentUsage,
      peak: this.memoryUsage.peakUsage,
      maxAllowed: this.memoryUsage.maxAllowed,
      utilizationPercentage: (this.memoryUsage.currentUsage / this.memoryUsage.maxAllowed) * 100,
    };
  }

  /**
   * Set memory limit
   */
  public static setMemoryLimit(limitBytes: number): void {
    this.memoryUsage.maxAllowed = limitBytes;
  }

  /**
   * Reset memory tracking
   */
  public static resetMemoryTracking(): void {
    this.memoryUsage.currentUsage = 0;
    this.memoryUsage.peakUsage = 0;
  }

  /**
   * Optimize content for knowledge base processing with memory management
   */
  public static optimizeContent(
    sources: Array<{ sourceId: string; sourceType: string; content: string }>,
    options: Partial<OptimizationOptions> = {}
  ): {
    chunks: ContentChunk[];
    result: OptimizationResult;
  } {
    const startTime = Date.now();
    const mergedOptions = { ...this.DEFAULT_OPTIONS, ...options };

    let allChunks: ContentChunk[] = [];
    let originalSize = 0;

    // Reset memory tracking for this operation
    this.resetMemoryTracking();

    // Process each source with memory management
    for (const source of sources) {
      originalSize += source.content.length;
      this.trackMemoryUsage('source_processing', source.content.length * 2); // Approximate memory for string processing

      try {
        // Clean content
        const cleanedContent = this.cleanContent(source.content, mergedOptions);

        // Create chunks in batches to manage memory
        const sourceChunks = this.createChunksWithMemoryManagement(
          cleanedContent,
          source.sourceId,
          source.sourceType,
          mergedOptions
        );

        allChunks.push(...sourceChunks);

        // Free memory after processing each source
        this.trackMemoryUsage('source_cleanup', source.content.length * 2, false);

        // Force garbage collection hint (if available in Node.js environment)
        if (typeof global !== 'undefined' && global.gc) {
          global.gc();
        }
      } catch (error) {
        console.error(`Error processing source ${source.sourceId}:`, error);
        this.trackMemoryUsage('source_cleanup', source.content.length * 2, false);
      }
    }

    const originalChunkCount = allChunks.length;
    let duplicatesRemoved = 0;

    // Deduplicate if enabled
    if (mergedOptions.enableDeduplication) {
      const deduplicationResult = this.deduplicateChunks(allChunks);
      allChunks = deduplicationResult.uniqueChunks;
      duplicatesRemoved = deduplicationResult.duplicatesRemoved;
    }

    const optimizedSize = allChunks.reduce((sum, chunk) => sum + chunk.size, 0);
    const processingTime = Date.now() - startTime;

    const result: OptimizationResult = {
      originalChunks: originalChunkCount,
      optimizedChunks: allChunks.length,
      duplicatesRemoved,
      totalSizeReduction: originalSize - optimizedSize,
      processingTime,
    };

    return { chunks: allChunks, result };
  }

  /**
   * Estimate memory usage for content processing
   */
  public static estimateMemoryUsage(
    contentSize: number,
    options: Partial<OptimizationOptions> = {}
  ): {
    estimatedChunks: number;
    estimatedMemoryMB: number;
    recommendedBatchSize: number;
  } {
    const mergedOptions = { ...this.DEFAULT_OPTIONS, ...options };
    const avgChunkSize = mergedOptions.maxChunkSize * 0.7; // Assume 70% of max size on average
    const estimatedChunks = Math.ceil(contentSize / avgChunkSize);
    
    // Rough memory estimation (content + overhead)
    const memoryMultiplier = 3; // Account for processing overhead
    const estimatedMemoryMB = (contentSize * memoryMultiplier) / (1024 * 1024);
    
    // Recommend batch size based on memory usage
    const maxMemoryPerBatch = 50; // 50MB per batch
    const recommendedBatchSize = Math.max(
      1,
      Math.floor(maxMemoryPerBatch / (estimatedMemoryMB / estimatedChunks))
    );

    return {
      estimatedChunks,
      estimatedMemoryMB,
      recommendedBatchSize,
    };
  }

  /**
   * Validate content before processing
   */
  public static validateContent(content: string): {
    isValid: boolean;
    warnings: string[];
    errors: string[];
    stats: {
      size: number;
      lines: number;
      words: number;
      characters: number;
    };
  } {
    const warnings: string[] = [];
    const errors: string[] = [];
    
    const stats = {
      size: content.length,
      lines: content.split('\n').length,
      words: content.split(/\s+/).filter(word => word.length > 0).length,
      characters: content.length,
    };

    // Validation checks
    if (content.length === 0) {
      errors.push('Content is empty');
    }

    if (content.length < 50) {
      warnings.push('Content is very short (less than 50 characters)');
    }

    if (content.length > 1000000) { // 1MB
      warnings.push('Content is very large (over 1MB) - consider splitting into smaller sources');
    }

    if (stats.words < 10) {
      warnings.push('Content has very few words (less than 10)');
    }

    // Check for potential encoding issues
    if (content.includes('\uFFFD')) {
      warnings.push('Content may have encoding issues (replacement characters detected)');
    }

    // Check for excessive repetition
    const uniqueLines = new Set(content.split('\n').map(line => line.trim()));
    if (uniqueLines.size < stats.lines * 0.5) {
      warnings.push('Content appears to have significant repetition');
    }

    return {
      isValid: errors.length === 0,
      warnings,
      errors,
      stats,
    };
  }
}
